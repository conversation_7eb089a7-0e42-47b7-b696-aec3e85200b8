import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "PressureWash Directory - Find Professional Pressure Washing Services",
  description:
    "The most comprehensive directory of pressure washing professionals in the United States. Find trusted local services for residential and commercial cleaning.",
  keywords: "pressure washing, power washing, exterior cleaning, residential cleaning, commercial cleaning",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>{children}</body>
    </html>
  )
}
