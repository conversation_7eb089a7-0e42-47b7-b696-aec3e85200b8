"use client"

import { useEffect, useRef } from "react"
import * as THREE from "three"

export function GlowingFlowingWaterBackground() {
  const mountRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!mountRef.current) return

    const scene = new THREE.Scene()
    const camera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1)
    const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true })
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
    mountRef.current.appendChild(renderer.domElement)

    // Full-screen triangle
    const geo = new THREE.PlaneGeometry(2, 2)
    const mat = new THREE.ShaderMaterial({
      transparent: true,
      depthWrite: false,
      uniforms: {
        uTime: { value: 0 },
        uRes:  { value: new THREE.Vector2() },
        uMouse:{ value: new THREE.Vector2(0.5, 0.5) }
      },
      vertexShader: `
        void main() {
          gl_Position = vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        precision highp float;
        uniform float uTime;
        uniform vec2  uRes;
        uniform vec2  uMouse;

        #define TAU 6.28318530718

        // Classic 2D noise
        vec2 hash(vec2 p) { p = vec2(dot(p,vec2(127.1,311.7)), dot(p,vec2(269.5,183.3))); return -1.0 + 2.0*fract(sin(p)*43758.5453123); }
        float noise(vec2 p) {
            vec2 i = floor(p), f = fract(p);
            vec2 u = f*f*(3.0-2.0*f);
            return mix(mix(dot(hash(i+vec2(0,0)),f-vec2(0,0)), dot(hash(i+vec2(1,0)),f-vec2(1,0)),u.x),
                       mix(dot(hash(i+vec2(0,1)),f-vec2(0,1)), dot(hash(i+vec2(1,1)),f-vec2(1,1)),u.x),u.y);
        }
        float fbm(vec2 p) {
            float v = 0.0, w = 0.5;
            for (int i=0;i<5;i++) { v += w*noise(p); p *= 2.0; w *= 0.5; }
            return v;
        }

        void main() {
            vec2 uv = gl_FragCoord.xy / uRes.xy;
            vec2 mouse = uMouse;

            // Parallax offset
            vec2 offset = (mouse - 0.5) * 0.15;

            // Flowing water surface
            vec2 p = uv*3.0 + vec2(uTime*0.08, uTime*0.04) + offset;
            float h = fbm(p) * 0.5 + fbm(p*2.2) * 0.25;

            // Glitter sparkles
            float sparkle = smoothstep(0.55,0.65,sin(h*30.0 + uTime*3.0))*0.9;
            sparkle *= pow(1.0-uv.y,2.0); // denser at top

            // Fresnel-like edge glow
            float fres = 1.0 - dot(normalize(uv - 0.5 + offset*0.5), vec2(0.0,1.0))*0.6;

            vec3 color = mix(vec3(0.8,0.95,1.0), vec3(1.0), sparkle + fres*0.4);
            float alpha = 0.25 + sparkle*0.7 + fres*0.2;

            gl_FragColor = vec4(color, alpha);
        }
      `
    })
    scene.add(new THREE.Mesh(geo, mat))

    const handleResize = () => {
      const { clientWidth, clientHeight } = mountRef.current!
      renderer.setSize(clientWidth, clientHeight)
      mat.uniforms.uRes.value.set(clientWidth, clientHeight)
    }
    handleResize()
    window.addEventListener("resize", handleResize)

    const handleMove = (e: PointerEvent) => {
      const { clientWidth, clientHeight } = mountRef.current!
      mat.uniforms.uMouse.value.set(e.clientX / clientWidth, 1.0 - e.clientY / clientHeight)
    }
    window.addEventListener("pointermove", handleMove)

    let then = 0
    const animate = (now: number) => {
      mat.uniforms.uTime.value = now * 0.001
      renderer.render(scene, camera)
      requestAnimationFrame(animate)
    }
    animate(0)

    return () => {
      window.removeEventListener("resize", handleResize)
      window.removeEventListener("pointermove", handleMove)
      mountRef.current?.removeChild(renderer.domElement)
      renderer.dispose()
    }
  }, [])

  return <div ref={mountRef} className="absolute inset-0 w-full h-full pointer-events-none" aria-hidden="true" />
}
