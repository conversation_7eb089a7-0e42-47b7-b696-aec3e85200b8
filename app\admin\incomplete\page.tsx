"use client"

import { DialogTrigger } from "@/components/ui/dialog"

import { useState, useEffect } from "react"
import type { IncompleteBusiness } from "@/types/business"
import { getIncompleteBusinesses } from "@/api/business"
import { updateIncompleteBusinessStatus } from "@/api/business"
import { Badge } from "@/components/ui/badge"
import { Clock } from "lucide-react"
import { MessageSquare } from "lucide-react"
import { CheckCircle } from "lucide-react"
import { XCircle } from "lucide-react"
import { ArrowLeft } from "lucide-react"
import { Eye } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import Link from "next/link"

export default function IncompleteBusinessListings() {
  const [businesses, setBusinesses] = useState<IncompleteBusiness[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedBusiness, setSelectedBusiness] = useState<IncompleteBusiness | null>(null)
  const [notes, setNotes] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  useEffect(() => {
    loadIncompleteBusinesses()
  }, [])

  const loadIncompleteBusinesses = async () => {
    try {
      const data = await getIncompleteBusinesses()
      setBusinesses(data)
    } catch (error) {
      console.error("Failed to load incomplete businesses:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (id: number, newStatus: string, businessNotes?: string) => {
    try {
      await updateIncompleteBusinessStatus(id, newStatus, businessNotes)
      await loadIncompleteBusinesses() // Refresh the list
    } catch (error) {
      console.error("Failed to update status:", error)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending_review":
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            Pending Review
          </Badge>
        )
      case "contacted":
        return (
          <Badge className="bg-blue-500">
            <MessageSquare className="h-3 w-3 mr-1" />
            Contacted
          </Badge>
        )
      case "resolved":
        return (
          <Badge className="bg-green-500">
            <CheckCircle className="h-3 w-3 mr-1" />
            Resolved
          </Badge>
        )
      case "rejected":
        return (
          <Badge className="bg-red-500">
            <XCircle className="h-3 w-3 mr-1" />
            Rejected
          </Badge>
        )
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const filteredBusinesses = businesses.filter((business) => statusFilter === "all" || business.status === statusFilter)

  const parseOriginalData = (jsonString: string) => {
    try {
      return JSON.parse(jsonString)
    } catch {
      return {}
    }
  }

  if (loading) {
    return <div className="min-h-screen bg-gray-50 flex items-center justify-center">Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/admin" className="flex items-center text-blue-600 hover:text-blue-800 mr-4">
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Admin
              </Link>
              <h1 className="text-2xl font-bold text-gray-900">Incomplete Business Listings</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending_review">Pending Review</SelectItem>
                  <SelectItem value="contacted">Contacted</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold">{businesses.length}</div>
              <p className="text-sm text-gray-600">Total Incomplete</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold">{businesses.filter((b) => b.status === "pending_review").length}</div>
              <p className="text-sm text-gray-600">Pending Review</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold">{businesses.filter((b) => b.status === "contacted").length}</div>
              <p className="text-sm text-gray-600">Contacted</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold">{businesses.filter((b) => b.status === "resolved").length}</div>
              <p className="text-sm text-gray-600">Resolved</p>
            </CardContent>
          </Card>
        </div>

        {/* Business List */}
        <Card>
          <CardHeader>
            <CardTitle>Incomplete Business Listings</CardTitle>
            <p className="text-sm text-gray-600">
              These businesses were found during import but are missing required information. Contact them to complete
              their listings.
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredBusinesses.map((business) => {
                const originalData = parseOriginalData(business.original_data)
                return (
                  <div key={business.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="font-semibold text-lg">{business.business_name}</h3>
                          {getStatusBadge(business.status)}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                          <div>
                            <p className="text-sm text-gray-600">Missing Fields:</p>
                            <p className="font-medium text-red-600">{business.missing_fields}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Available Info:</p>
                            <div className="text-sm">
                              {originalData.phone && <p>Phone: {originalData.phone}</p>}
                              {originalData.website && <p>Website: {originalData.website}</p>}
                              {originalData.city && <p>City: {originalData.city}</p>}
                              {originalData.state && <p>State: {originalData.state}</p>}
                            </div>
                          </div>
                        </div>

                        {business.notes && (
                          <div className="mb-3">
                            <p className="text-sm text-gray-600">Notes:</p>
                            <p className="text-sm bg-gray-50 p-2 rounded">{business.notes}</p>
                          </div>
                        )}

                        <p className="text-xs text-gray-500">
                          Created: {new Date(business.created_at).toLocaleDateString()}
                          {business.updated_at && ` • Updated: ${new Date(business.updated_at).toLocaleDateString()}`}
                        </p>
                      </div>

                      <div className="flex space-x-2 ml-4">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm" onClick={() => setSelectedBusiness(business)}>
                              <Eye className="h-4 w-4 mr-1" />
                              View Details
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Business Details: {business.business_name}</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div>
                                <Label>Original Data:</Label>
                                <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40">
                                  {JSON.stringify(originalData, null, 2)}
                                </pre>
                              </div>
                              <div>
                                <Label htmlFor="notes">Add Notes:</Label>
                                <Textarea
                                  id="notes"
                                  value={notes}
                                  onChange={(e) => setNotes(e.target.value)}
                                  placeholder="Add notes about contact attempts, missing info, etc..."
                                  className="mt-1"
                                />
                              </div>
                              <div className="flex space-x-2">
                                <Button
                                  onClick={() => handleStatusUpdate(business.id, "contacted", notes)}
                                  className="bg-blue-500"
                                >
                                  Mark as Contacted
                                </Button>
                                <Button
                                  onClick={() => handleStatusUpdate(business.id, "resolved", notes)}
                                  className="bg-green-500"
                                >
                                  Mark as Resolved
                                </Button>
                                <Button
                                  onClick={() => handleStatusUpdate(business.id, "rejected", notes)}
                                  variant="destructive"
                                >
                                  Reject
                                </Button>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>

                        <Select
                          value={business.status}
                          onValueChange={(value) => handleStatusUpdate(business.id, value)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="pending_review">Pending</SelectItem>
                            <SelectItem value="contacted">Contacted</SelectItem>
                            <SelectItem value="resolved">Resolved</SelectItem>
                            <SelectItem value="rejected">Rejected</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                )
              })}

              {filteredBusinesses.length === 0 && (
                <div className="text-center py-12 text-gray-500">
                  No incomplete businesses found for the selected filter.
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
