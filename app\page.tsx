"use client"

import { useState } from "react"
import { Search, MapPin, Star, Phone, Globe } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { GlisteningWaterBackground } from "@/components/glistening-water-background" // Updated import

// Mock data for demonstration
const mockBusinesses = [
  {
    id: "1",
    name: "Elite Pressure Washing",
    city: "Miami",
    state: "FL",
    description: "Professional residential and commercial pressure washing services with 15+ years of experience.",
    phone: "(*************",
    email: "<EMAIL>",
    website: "www.elitepressurewashing.com",
    rating: 4.8,
    reviewCount: 127,
    services: ["Residential", "Commercial", "Roof Cleaning", "Driveway Cleaning"],
    claimed: true,
    image: "/placeholder.svg?height=200&width=300",
  },
  {
    id: "2",
    name: "Crystal Clean Power Wash",
    city: "Orlando",
    state: "FL",
    description: "Eco-friendly pressure washing solutions for homes and businesses throughout Central Florida.",
    phone: "(*************",
    email: "<EMAIL>",
    website: "www.crystalcleanpw.com",
    rating: 4.6,
    reviewCount: 89,
    services: ["Residential", "Commercial", "Pool Deck Cleaning"],
    claimed: false,
    image: "/placeholder.svg?height=200&width=300",
  },
  {
    id: "3",
    name: "ProWash Solutions",
    city: "Tampa",
    state: "FL",
    description: "Complete exterior cleaning services including pressure washing, soft washing, and gutter cleaning.",
    phone: "(*************",
    email: "<EMAIL>",
    website: "www.prowashsolutions.com",
    rating: 4.9,
    reviewCount: 203,
    services: ["Residential", "Commercial", "Soft Washing", "Gutter Cleaning"],
    claimed: true,
    image: "/placeholder.svg?height=200&width=300",
  },
]

const popularStates = [
  { code: "FL", name: "Florida", count: 1247 },
  { code: "TX", name: "Texas", count: 1156 },
  { code: "CA", name: "California", count: 1089 },
  { code: "NY", name: "New York", count: 892 },
  { code: "GA", name: "Georgia", count: 743 },
  { code: "NC", name: "North Carolina", count: 678 },
]

export default function HomePage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedState, setSelectedState] = useState("")
  const [businesses, setBusinesses] = useState(mockBusinesses)

  const handleSearch = () => {
    // In a real app, this would make an API call
    const filtered = mockBusinesses.filter(
      (business) =>
        business.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        business.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
        business.state.toLowerCase().includes(searchTerm.toLowerCase()),
    )
    setBusinesses(filtered)
  }

  return (
    <div className="min-h-screen bg-gray-950">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">PressureWash Directory</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/admin">
                <Button variant="outline">Admin</Button>
              </Link>
              <Button>List Your Business</Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 px-4 overflow-hidden min-h-[500px] bg-gray-950">
        <GlisteningWaterBackground
          waterColor="#0055aa"
          foamColor="#ffffff"
          sunColor="#ffdd99"
          fogColor="#002244"
          sunDirection={[0.6, 0.9, 0.4]}
          reflectivity={0.85}
        /> {/* Customized ocean with props */}
        <div className="relative z-10 max-w-4xl mx-auto text-center">
          <h2 className="text-5xl font-bold text-white mb-6">Find Professional Pressure Washing Services</h2>{" "}
          {/* Text color changed to white */}
          <p className="text-xl text-gray-300 mb-12">
            Connect with trusted pressure washing professionals in your area
          </p>{" "}
          {/* Text color changed to gray-300 */}
          {/* Search Bar */}
          <div className="bg-white rounded-2xl shadow-xl p-8 mb-12">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <Input
                    placeholder="Enter city, state, or ZIP code"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-12 text-lg"
                  />
                </div>
              </div>
              <Button onClick={handleSearch} size="lg" className="h-12 px-8">
                <Search className="mr-2 h-5 w-5" />
                Search
              </Button>
            </div>
          </div>
          {/* Popular States */}
          <div className="mb-16">
            <h3 className="text-2xl font-semibold text-white mb-8">Popular Locations</h3>{" "}
            {/* Text color changed to white */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {popularStates.map((state) => (
                <Link key={state.code} href={`/state/${state.code.toLowerCase()}`}>
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-blue-600">{state.code}</div>
                      <div className="text-sm text-gray-600">{state.name}</div>
                      <div className="text-xs text-gray-500">{state.count} businesses</div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>
      {/* Featured Businesses */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Featured Businesses</h3>
            <p className="text-lg text-gray-600">Top-rated pressure washing services</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {businesses.map((business) => (
              <Card key={business.id} className="hover:shadow-xl transition-shadow">
                <div className="relative">
                  <img
                    src={business.image || "/placeholder.svg"}
                    alt={business.name}
                    className="w-full h-48 object-cover rounded-t-lg"
                  />
                  {business.claimed && <Badge className="absolute top-2 right-2 bg-green-500">Verified</Badge>}
                </div>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-xl">{business.name}</CardTitle>
                    <div className="flex items-center">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="ml-1 text-sm font-medium">{business.rating}</span>
                      <span className="ml-1 text-sm text-gray-500">({business.reviewCount})</span>
                    </div>
                  </div>
                  <div className="flex items-center text-gray-600">
                    <MapPin className="h-4 w-4 mr-1" />
                    {business.city}, {business.state}
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4 line-clamp-2">{business.description}</p>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {business.services.slice(0, 3).map((service) => (
                      <Badge key={service} variant="secondary">
                        {service}
                      </Badge>
                    ))}
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-sm text-gray-600">
                      <Phone className="h-4 w-4 mr-2" />
                      {business.phone}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Globe className="h-4 w-4 mr-2" />
                      {business.website}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Link href={`/business/${business.id}`} className="flex-1">
                      <Button variant="outline" className="w-full bg-transparent">
                        View Details
                      </Button>
                    </Link>
                    {!business.claimed && (
                      <Link href={`/claim/${business.id}`}>
                        <Button variant="default">Claim</Button>
                      </Link>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h4 className="text-lg font-semibold mb-4">PressureWash Directory</h4>
              <p className="text-gray-400">
                The most comprehensive directory of pressure washing professionals in the United States.
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">For Businesses</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/list-business" className="hover:text-white">
                    List Your Business
                  </Link>
                </li>
                <li>
                  <Link href="/claim" className="hover:text-white">
                    Claim Your Listing
                  </Link>
                </li>
                <li>
                  <Link href="/pricing" className="hover:text-white">
                    Pricing
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Browse by State</h4>
              <ul className="space-y-2 text-gray-400">
                {popularStates.slice(0, 4).map((state) => (
                  <li key={state.code}>
                    <Link href={`/state/${state.code.toLowerCase()}`} className="hover:text-white">
                      {state.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/contact" className="hover:text-white">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="/help" className="hover:text-white">
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="hover:text-white">
                    Privacy Policy
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 PressureWash Directory. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
