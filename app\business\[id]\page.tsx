"use client"

import { useState } from "react"
import { ArrowLeft, Star, MapPin, Phone, Globe, Mail, Clock, Shield, Award } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import Link from "next/link"
import { useParams } from "next/navigation"

// Mock business data
const mockBusiness = {
  id: "1",
  name: "Elite Pressure Washing",
  city: "Miami",
  state: "FL",
  address: "123 Business Ave, Miami, FL 33101",
  description:
    "Elite Pressure Washing has been serving the Miami area for over 15 years with professional residential and commercial pressure washing services. We use eco-friendly cleaning solutions and state-of-the-art equipment to deliver exceptional results.",
  phone: "(*************",
  email: "<EMAIL>",
  website: "www.elitepressurewashing.com",
  rating: 4.8,
  reviewCount: 127,
  services: [
    "Residential Pressure Washing",
    "Commercial Cleaning",
    "Roof Cleaning",
    "Driveway Cleaning",
    "Pool Deck Cleaning",
    "Soft Washing",
  ],
  claimed: true,
  hours: {
    monday: "8:00 AM - 6:00 PM",
    tuesday: "8:00 AM - 6:00 PM",
    wednesday: "8:00 AM - 6:00 PM",
    thursday: "8:00 AM - 6:00 PM",
    friday: "8:00 AM - 6:00 PM",
    saturday: "9:00 AM - 4:00 PM",
    sunday: "Closed",
  },
  gallery: [
    "/placeholder.svg?height=400&width=600",
    "/placeholder.svg?height=400&width=600",
    "/placeholder.svg?height=400&width=600",
    "/placeholder.svg?height=400&width=600",
  ],
  reviews: [
    {
      id: 1,
      name: "John Smith",
      rating: 5,
      date: "2024-01-15",
      comment:
        "Excellent service! They cleaned our driveway and it looks brand new. Very professional and reasonably priced.",
    },
    {
      id: 2,
      name: "Sarah Johnson",
      rating: 5,
      date: "2024-01-10",
      comment: "Elite Pressure Washing did an amazing job on our commercial building. Highly recommend!",
    },
    {
      id: 3,
      name: "Mike Davis",
      rating: 4,
      date: "2024-01-05",
      comment: "Great work on our roof cleaning. The team was punctual and thorough.",
    },
  ],
}

export default function BusinessDetailPage() {
  const params = useParams()
  const [selectedImage, setSelectedImage] = useState(0)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center text-blue-600 hover:text-blue-800">
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Directory
            </Link>
            <div className="flex items-center space-x-4">
              {!mockBusiness.claimed && (
                <Link href={`/claim/${params.id}`}>
                  <Button>Claim This Business</Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Business Header */}
            <Card className="mb-8">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-3xl mb-2">{mockBusiness.name}</CardTitle>
                    <div className="flex items-center mb-2">
                      <div className="flex items-center mr-4">
                        <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                        <span className="ml-1 text-lg font-medium">{mockBusiness.rating}</span>
                        <span className="ml-1 text-gray-500">({mockBusiness.reviewCount} reviews)</span>
                      </div>
                      {mockBusiness.claimed && (
                        <Badge className="bg-green-500">
                          <Shield className="h-3 w-3 mr-1" />
                          Verified Business
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center text-gray-600">
                      <MapPin className="h-4 w-4 mr-1" />
                      {mockBusiness.address}
                    </div>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Gallery */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>Gallery</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-4">
                  <img
                    src={mockBusiness.gallery[selectedImage] || "/placeholder.svg"}
                    alt="Business gallery"
                    className="w-full h-96 object-cover rounded-lg"
                  />
                  <div className="grid grid-cols-4 gap-2">
                    {mockBusiness.gallery.map((image, index) => (
                      <img
                        key={index}
                        src={image || "/placeholder.svg"}
                        alt={`Gallery ${index + 1}`}
                        className={`h-20 object-cover rounded cursor-pointer ${
                          selectedImage === index ? "ring-2 ring-blue-500" : ""
                        }`}
                        onClick={() => setSelectedImage(index)}
                      />
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Tabs */}
            <Tabs defaultValue="about" className="mb-8">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="about">About</TabsTrigger>
                <TabsTrigger value="services">Services</TabsTrigger>
                <TabsTrigger value="reviews">Reviews</TabsTrigger>
              </TabsList>

              <TabsContent value="about">
                <Card>
                  <CardHeader>
                    <CardTitle>About {mockBusiness.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 leading-relaxed">{mockBusiness.description}</p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="services">
                <Card>
                  <CardHeader>
                    <CardTitle>Services Offered</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {mockBusiness.services.map((service, index) => (
                        <div key={index} className="flex items-center p-3 bg-blue-50 rounded-lg">
                          <Award className="h-5 w-5 text-blue-600 mr-3" />
                          <span className="font-medium">{service}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="reviews">
                <Card>
                  <CardHeader>
                    <CardTitle>Customer Reviews</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {mockBusiness.reviews.map((review) => (
                        <div key={review.id} className="border-b pb-4 last:border-b-0">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center">
                              <span className="font-medium">{review.name}</span>
                              <div className="flex items-center ml-2">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`h-4 w-4 ${
                                      i < review.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                                    }`}
                                  />
                                ))}
                              </div>
                            </div>
                            <span className="text-sm text-gray-500">{review.date}</span>
                          </div>
                          <p className="text-gray-600">{review.comment}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Contact Information */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center">
                  <Phone className="h-5 w-5 text-blue-600 mr-3" />
                  <div>
                    <div className="font-medium">{mockBusiness.phone}</div>
                    <div className="text-sm text-gray-500">Phone</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-blue-600 mr-3" />
                  <div>
                    <div className="font-medium">{mockBusiness.email}</div>
                    <div className="text-sm text-gray-500">Email</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <Globe className="h-5 w-5 text-blue-600 mr-3" />
                  <div>
                    <div className="font-medium">{mockBusiness.website}</div>
                    <div className="text-sm text-gray-500">Website</div>
                  </div>
                </div>
                <div className="pt-4">
                  <Button className="w-full mb-2">Get Quote</Button>
                  <Button variant="outline" className="w-full bg-transparent">
                    Visit Website
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Business Hours */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Business Hours
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(mockBusiness.hours).map(([day, hours]) => (
                    <div key={day} className="flex justify-between">
                      <span className="capitalize font-medium">{day}</span>
                      <span className="text-gray-600">{hours}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
