"use client"

import { useRef, useEffect, useMemo } from "react"
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from "@react-three/fiber"
import * as THREE from "three"

// TypeScript interface for customizable ocean properties
export interface OceanPalette {
  waterColor?:   THREE.ColorRepresentation;
  foamColor?:    THREE.ColorRepresentation;
  sunColor?:     THREE.ColorRepresentation;
  fogColor?:     THREE.ColorRepresentation;
  sunDirection?: [x: number, y: number, z: number];
  reflectivity?: number; // 0–1
}

// Advanced ocean shader with Gerstner waves and realistic lighting
const OceanMaterial = new THREE.ShaderMaterial({
  uniforms: {
    uTime: { value: 0 },
    uResolution: { value: new THREE.Vector2() },
    uMouse: { value: new THREE.Vector2(0.5, 0.5) },
    uCameraPos: { value: new THREE.Vector3() },
    uWaterColor: { value: new THREE.Color("#0088cc") },
    uFoamColor: { value: new THREE.Color("#ffffff") },
    uSunColor: { value: new THREE.Color("#ffeeaa") },
    uFogColor: { value: new THREE.Color("#003366") },
    uSunDir: { value: new THREE.Vector3(0.7, 0.8, 0.5) },
    uReflectivity: { value: 0.8 },
  },
  transparent: true,
  depthWrite: false,
  vertexShader: `
    varying vec2 vUv;
    void main() {
      vUv = uv;
      gl_Position = vec4(position, 1.0);
    }
  `,
  fragmentShader: `
    precision highp float;
    uniform float uTime;
    uniform vec2 uResolution;
    uniform vec2 uMouse;
    uniform vec3 uWaterColor;
    uniform vec3 uFoamColor;
    uniform vec3 uSunColor;
    uniform vec3 uFogColor;
    uniform vec3 uSunDir;
    uniform float uReflectivity;
    varying vec2 vUv;

    #define PI 3.14159265359
    #define TAU 6.28318530718

    // Advanced hash functions for better randomness
    vec3 hash3(vec2 p) {
      vec3 q = vec3(dot(p,vec2(127.1,311.7)),
                    dot(p,vec2(269.5,183.3)),
                    dot(p,vec2(419.2,371.9)));
      return fract(sin(q)*43758.5453);
    }

    vec2 hash(vec2 p) {
      p = vec2(dot(p,vec2(127.1,311.7)), dot(p,vec2(269.5,183.3)));
      return -1.0 + 2.0*fract(sin(p)*43758.5453123);
    }

    // Improved noise with better interpolation
    float noise(vec2 p) {
      vec2 i = floor(p), f = fract(p);
      vec2 u = f*f*f*(f*(f*6.0-15.0)+10.0); // Quintic interpolation
      return mix(mix(dot(hash(i+vec2(0,0)),f-vec2(0,0)),
                     dot(hash(i+vec2(1,0)),f-vec2(1,0)),u.x),
                 mix(dot(hash(i+vec2(0,1)),f-vec2(0,1)),
                     dot(hash(i+vec2(1,1)),f-vec2(1,1)),u.x),u.y);
    }

    // Multi-octave noise with domain warping
    float fbm(vec2 p) {
      float v = 0.0, w = 0.5, t = 0.0;
      for (int i = 0; i < 6; i++) {
        v += w * noise(p);
        t += w;
        p *= 2.17; // Non-integer scaling to avoid artifacts
        w *= 0.5;
      }
      return v / t;
    }

    // Gerstner wave function for realistic water waves
    vec3 gerstnerWave(vec2 pos, vec2 direction, float amplitude, float frequency, float speed, float steepness) {
      float phase = frequency * dot(direction, pos) + speed * uTime;
      float c = cos(phase);
      float s = sin(phase);

      return vec3(
        steepness * amplitude * direction.x * c,
        steepness * amplitude * direction.y * c,
        amplitude * s
      );
    }

    void main() {
      vec2 uv = vUv;
      vec2 screenPos = uv * 2.0 - 1.0;

      // Enhanced mouse interaction
      vec2 mouseOffset = (uMouse - 0.5) * 0.15;
      float mouseDistance = length(uv - uMouse);
      float mouseInfluence = exp(-mouseDistance * 3.0) * 0.8;

      // Multiple Gerstner waves for realistic water motion
      vec3 wave1 = gerstnerWave(uv * 8.0, normalize(vec2(1.0, 0.3)), 0.08, 2.0, 1.2, 0.6);
      vec3 wave2 = gerstnerWave(uv * 6.0, normalize(vec2(-0.7, 1.0)), 0.06, 1.5, 0.9, 0.8);
      vec3 wave3 = gerstnerWave(uv * 12.0, normalize(vec2(0.5, -0.8)), 0.04, 3.0, 1.8, 0.4);
      vec3 wave4 = gerstnerWave(uv * 15.0, normalize(vec2(-0.2, 0.9)), 0.03, 4.0, 2.2, 0.3);

      // Combine waves with mouse influence
      vec3 totalWave = wave1 + wave2 + wave3 + wave4;
      totalWave *= (1.0 + mouseInfluence * 0.5);

      // Advanced surface normal calculation
      vec2 epsilon = vec2(0.01, 0.0);
      float heightL = totalWave.z;
      float heightR = (gerstnerWave((uv + epsilon.xy) * 8.0, normalize(vec2(1.0, 0.3)), 0.08, 2.0, 1.2, 0.6) +
                      gerstnerWave((uv + epsilon.xy) * 6.0, normalize(vec2(-0.7, 1.0)), 0.06, 1.5, 0.9, 0.8)).z;
      float heightT = (gerstnerWave((uv + epsilon.yx) * 8.0, normalize(vec2(1.0, 0.3)), 0.08, 2.0, 1.2, 0.6) +
                      gerstnerWave((uv + epsilon.yx) * 6.0, normalize(vec2(-0.7, 1.0)), 0.06, 1.5, 0.9, 0.8)).z;

      vec3 normal = normalize(vec3(heightL - heightR, heightL - heightT, epsilon.x * 2.0));

      // Enhanced noise layers with domain warping
      vec2 warpedUV = uv + totalWave.xy * 0.1 + mouseOffset;
      float noise1 = fbm(warpedUV * 4.0 + uTime * 0.1);
      float noise2 = fbm(warpedUV * 8.0 + vec2(uTime * 0.15, -uTime * 0.08));
      float noise3 = fbm(warpedUV * 16.0 + vec2(-uTime * 0.12, uTime * 0.18));

      // Caustics simulation
      vec2 causticUV = warpedUV * 3.0 + totalWave.xy * 0.2;
      float caustic1 = abs(sin(causticUV.x * 10.0 + uTime * 2.0)) * abs(sin(causticUV.y * 10.0 + uTime * 1.5));
      float caustic2 = abs(sin(causticUV.x * 15.0 - uTime * 1.8)) * abs(sin(causticUV.y * 15.0 + uTime * 2.2));
      float caustics = pow(caustic1 * caustic2, 0.5) * 0.3;

      // Fresnel effect for realistic water reflections
      vec3 viewDir = normalize(vec3(screenPos, 1.0));
      float fresnel = pow(1.0 - max(0.0, dot(normal, viewDir)), 2.0);
      fresnel = mix(0.02, 1.0, fresnel);

      // Dynamic depth calculation
      float depth = 0.5 + (noise1 * 0.3 + noise2 * 0.2 + noise3 * 0.1) + totalWave.z;
      depth = smoothstep(0.2, 0.8, depth);

      // Advanced water coloring
      vec3 deepColor = mix(uWaterColor, uFogColor, 0.4);
      vec3 shallowColor = mix(uWaterColor, uFoamColor, 0.1);
      vec3 waterColor = mix(deepColor, shallowColor, depth);

      // Enhanced sun reflection with wave normals
      vec3 sunDir = normalize(uSunDir + vec3(mouseOffset * 0.3, 0.0));
      vec3 reflectDir = reflect(-sunDir, normal);
      float sunReflection = pow(max(0.0, dot(reflectDir, viewDir)), 32.0) * uReflectivity;

      // Dynamic foam generation based on wave steepness
      float waveGradient = length(vec2(heightL - heightR, heightL - heightT));
      float foam = smoothstep(0.1, 0.3, waveGradient) * 0.4;
      foam += smoothstep(0.8, 1.0, caustics) * 0.2;

      // Mouse interaction effects
      float mouseRipples = sin(mouseDistance * 25.0 - uTime * 4.0) * mouseInfluence * 0.1;
      float mouseGlow = mouseInfluence * 0.15;

      // Final color composition
      vec3 finalColor = waterColor;
      finalColor = mix(finalColor, uSunColor, sunReflection * 0.8);
      finalColor = mix(finalColor, uSunColor, caustics * 0.6);
      finalColor = mix(finalColor, uFoamColor, foam);
      finalColor = mix(finalColor, uSunColor, mouseGlow);
      finalColor += vec3(mouseRipples * 0.5);

      // Enhanced transparency with depth
      float alpha = 0.85 + fresnel * 0.1 + foam * 0.05;

      gl_FragColor = vec4(finalColor, alpha);
    }
  `
})

// React Three Fiber Ocean Component
function Ocean({ palette }: { palette: OceanPalette }) {
  const meshRef = useRef<THREE.Mesh>(null!)
  const { size, clock, camera, pointer } = useThree()

  // Clone the material to avoid sharing between instances
  const material = useMemo(() => OceanMaterial.clone(), [])

  // Apply palette colors when they change
  useEffect(() => {
    const waterColor = palette.waterColor ?? "#0088cc"
    const foamColor = palette.foamColor ?? "#ffffff"
    const sunColor = palette.sunColor ?? "#ffeeaa"
    const fogColor = palette.fogColor ?? "#003366"

    console.log('Setting colors:', { waterColor, foamColor, sunColor, fogColor })

    material.uniforms.uWaterColor.value.set(waterColor)
    material.uniforms.uFoamColor.value.set(foamColor)
    material.uniforms.uSunColor.value.set(sunColor)
    material.uniforms.uFogColor.value.set(fogColor)
    material.uniforms.uSunDir.value.set(...(palette.sunDirection ?? [0.7, 0.8, 0.5]))
    material.uniforms.uReflectivity.value = palette.reflectivity ?? 0.8

    console.log('Color values after setting:', {
      water: material.uniforms.uWaterColor.value,
      foam: material.uniforms.uFoamColor.value,
      sun: material.uniforms.uSunColor.value,
      fog: material.uniforms.uFogColor.value
    })
  }, [palette, material])

  useFrame(() => {
    if (!meshRef.current) return

    material.uniforms.uTime.value = clock.elapsedTime
    material.uniforms.uResolution.value.set(size.width, size.height)
    material.uniforms.uMouse.value.set(
      (pointer.x + 1) * 0.5,
      (pointer.y + 1) * 0.5
    )
    material.uniforms.uCameraPos.value.copy(camera.position)

    // Debug: Log color values once to ensure they're being set
    if (clock.elapsedTime > 0.5 && clock.elapsedTime < 1) {
      console.log('Water color:', material.uniforms.uWaterColor.value)
      console.log('Foam color:', material.uniforms.uFoamColor.value)
      console.log('Sun color:', material.uniforms.uSunColor.value)
    }
  })

  return (
    <mesh ref={meshRef} material={material}>
      <planeGeometry args={[2, 2]} />
    </mesh>
  )
}

export function GlisteningWaterBackground(props: OceanPalette = {}) {
  return (
    <div className="absolute inset-0 w-full h-full pointer-events-none" aria-hidden="true">
      <Canvas
        camera={{ position: [0, 0, 1], fov: 60 }}
        gl={{ alpha: true, antialias: true, powerPreference: "high-performance" }}
        dpr={[1, 2]}
        style={{ background: 'transparent' }}
      >
        <Ocean palette={props} />
      </Canvas>
    </div>
  )
}
