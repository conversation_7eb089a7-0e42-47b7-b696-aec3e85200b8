"use server"

import { createClient } from "@supabase/supabase-js"

const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!)

// Helper function for delay (re-using from import-businesses.ts)
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

// Helper function for retries (re-using from import-businesses.ts)
async function retrySupabaseCall<T>(
  call: () => Promise<{ data: T | null; error: any }>,
  maxRetries = 5,
  initialDelay = 200,
): Promise<{ data: T | null; error: any }> {
  let retries = 0
  while (retries < maxRetries) {
    try {
      const result = await call()
      if (!result.error || result.error.code === "PGRST116") {
        return result
      }
      console.warn(
        `Supabase call failed (retry ${retries + 1}/${maxRetries}): Supabase Error - ${result.error.message}`,
      )
      retries++
      await delay(initialDelay * Math.pow(2, retries - 1))
    } catch (e: any) {
      console.warn(`Supabase call failed (retry ${retries + 1}/${maxRetries}): General Error - ${e.message}`)
      retries++
      await delay(initialDelay * Math.pow(2, retries - 1))
    }
  }
  return call()
}

export async function listBusiness(formData: FormData) {
  const businessName = formData.get("businessName") as string
  const streetAddress = formData.get("streetAddress") as string
  const city = formData.get("city") as string
  const stateCode = formData.get("stateCode") as string
  const zipCode = formData.get("zipCode") as string | null
  const phone = formData.get("phone") as string | null
  const email = formData.get("email") as string | null
  const website = formData.get("website") as string | null
  const description = formData.get("description") as string | null
  const servicesInput = formData.get("services") as string | null

  // Basic validation
  if (!businessName || !streetAddress || !city || !stateCode) {
    return { success: false, message: "Missing required fields: Business Name, Street Address, City, State." }
  }

  let stateId: number | null = null
  let cityId: number | null = null

  try {
    // 1. Find or create State
    const { data: stateData, error: stateLookupError } = await retrySupabaseCall(() =>
      supabase.from("states").select("id").eq("code", stateCode).single(),
    )

    if (stateLookupError && stateLookupError.code === "PGRST116") {
      // State not found, attempt to insert (using stateCode as name for simplicity if full name not available)
      const { data: newStatData, error: insertStateError } = await retrySupabaseCall(() =>
        supabase.from("states").insert({ name: stateCode, code: stateCode }).select("id").single(),
      )
      if (insertStateError) {
        console.error("Error inserting new state:", insertStateError)
        return { success: false, message: `Failed to resolve state: ${insertStateError.message}` }
      }
      stateId = newStatData?.id || null
    } else if (stateLookupError) {
      console.error("Supabase state lookup error:", stateLookupError)
      return { success: false, message: `Failed to lookup state: ${stateLookupError.message}` }
    } else {
      stateId = stateData?.id || null
    }

    if (!stateId) {
      return { success: false, message: "Could not resolve state ID." }
    }

    // 2. Find or create City
    const { data: cityData, error: cityLookupError } = await retrySupabaseCall(() =>
      supabase.from("cities").select("id").eq("name", city).eq("state_id", stateId).single(),
    )

    if (cityLookupError && cityLookupError.code === "PGRST116") {
      // City not found, attempt to insert
      const { data: newCityData, error: insertCityError } = await retrySupabaseCall(() =>
        supabase.from("cities").insert({ name: city, state_id: stateId }).select("id").single(),
      )
      if (insertCityError) {
        console.error("Error inserting new city:", insertCityError)
        return { success: false, message: `Failed to resolve city: ${insertCityError.message}` }
      }
      cityId = newCityData?.id || null
    } else if (cityLookupError) {
      console.error("Supabase city lookup error:", cityLookupError)
      return { success: false, message: `Failed to lookup city: ${cityLookupError.message}` }
    } else {
      cityId = cityData?.id || null
    }

    if (!cityId) {
      return { success: false, message: "Could not resolve city ID." }
    }

    // Generate slug
    const slug =
      businessName
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/(^-|-$)/g, "") + `-${Math.random().toString(36).substring(2, 8)}` // Add random suffix to ensure uniqueness

    // 3. Insert Business
    const { data: businessInsertData, error: businessInsertError } = await retrySupabaseCall(() =>
      supabase
        .from("businesses")
        .insert({
          name: businessName,
          slug: slug,
          address: streetAddress,
          city_id: cityId,
          state_id: stateId, // Ensure state_id is included
          zip_code: zipCode,
          phone: phone,
          email: email,
          website: website,
          description: description,
          is_claimed: false, // New listings are not claimed by default
          verified: false,
        })
        .select("id")
        .single(),
    )

    if (businessInsertError) {
      console.error("Error inserting business:", businessInsertError)
      return { success: false, message: `Failed to list business: ${businessInsertError.message}` }
    }

    const businessId = businessInsertData?.id

    if (!businessId) {
      return { success: false, message: "Failed to retrieve new business ID." }
    }

    // 4. Insert Services (if provided)
    if (servicesInput) {
      const serviceNames = servicesInput
        .split(",")
        .map((s) => s.trim())
        .filter(Boolean)
      for (const serviceName of serviceNames) {
        let serviceId: number | null = null
        const { data: serviceData, error: serviceLookupError } = await retrySupabaseCall(() =>
          supabase.from("services").select("id").eq("name", serviceName).single(),
        )

        if (serviceLookupError && serviceLookupError.code === "PGRST116") {
          // Service not found, insert it
          const { data: newServiceData, error: insertServiceError } = await retrySupabaseCall(() =>
            supabase.from("services").insert({ name: serviceName }).select("id").single(),
          )
          if (insertServiceError) {
            console.error(`Error inserting service ${serviceName}:`, insertServiceError)
            continue // Skip to next service if insertion fails
          }
          serviceId = newServiceData?.id || null
        } else if (serviceLookupError) {
          console.error(`Supabase service lookup error for ${serviceName}:`, serviceLookupError)
          continue
        } else {
          serviceId = serviceData?.id || null
        }

        if (serviceId) {
          const { error: businessServiceError } = await retrySupabaseCall(() =>
            supabase.from("business_services").insert({ business_id: businessId, service_id: serviceId }),
          )
          if (businessServiceError) {
            console.error(`Error linking business ${businessId} to service ${serviceId}:`, businessServiceError)
          }
        }
      }
    }

    // 5. Insert Business Hours (example for Monday, you'd extend this for all days)
    const daysOfWeek = ["sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"]
    for (let i = 0; i < daysOfWeek.length; i++) {
      const day = daysOfWeek[i]
      const openTime = formData.get(`${day}Open`) as string | null
      const closeTime = formData.get(`${day}Close`) as string | null
      const isClosed = formData.get(`${day}Closed`) === "on"

      if (isClosed) {
        const { error: hoursError } = await retrySupabaseCall(() =>
          supabase.from("business_hours").insert({
            business_id: businessId,
            day_of_week: i,
            is_closed: true,
          }),
        )
        if (hoursError) console.error(`Error inserting closed hours for ${day}:`, hoursError)
      } else if (openTime && closeTime) {
        const { error: hoursError } = await retrySupabaseCall(() =>
          supabase.from("business_hours").insert({
            business_id: businessId,
            day_of_week: i,
            open_time: openTime,
            close_time: closeTime,
            is_closed: false,
          }),
        )
        if (hoursError) console.error(`Error inserting hours for ${day}:`, hoursError)
      }
    }

    return {
      success: true,
      message: "Business listed successfully! It will be reviewed before appearing in the directory.",
    }
  } catch (error: any) {
    console.error("Unhandled error in listBusiness action:", error)
    return { success: false, message: `An unexpected error occurred: ${error.message}` }
  }
}
