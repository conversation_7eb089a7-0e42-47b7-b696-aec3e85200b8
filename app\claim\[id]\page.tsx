"use client"

import type React from "react"

import { useState } from "react"
import { ArrowLeft, Upload, CheckCircle, AlertCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Checkbox } from "@/components/ui/checkbox"
import Link from "next/link"
import { useParams } from "next/navigation"

// Mock business data
const mockBusiness = {
  id: "1",
  name: "Elite Pressure Washing",
  address: "123 Business Ave, Miami, FL 33101",
  phone: "(*************",
  city: "Miami",
  state: "FL",
}

export default function ClaimBusinessPage() {
  const params = useParams()
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    relationship: "",
    businessEmail: "",
    businessPhone: "",
    message: "",
  })
  const [documents, setDocuments] = useState<File[]>([])
  const [agreed, setAgreed] = useState(false)
  const [submitted, setSubmitted] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setDocuments([...documents, ...Array.from(e.target.files)])
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!agreed) return

    // Simulate form submission
    setSubmitted(true)
  }

  if (submitted) {
    return (
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link href="/" className="flex items-center text-blue-600 hover:text-blue-800">
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Directory
              </Link>
            </div>
          </div>
        </header>

        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <Card>
            <CardContent className="p-8 text-center">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-gray-900 mb-4">Claim Request Submitted Successfully!</h1>
              <p className="text-gray-600 mb-6">
                Thank you for claiming your business listing. We'll review your request and supporting documents within
                2-3 business days.
              </p>
              <div className="bg-blue-50 p-4 rounded-lg mb-6">
                <h3 className="font-semibold text-blue-900 mb-2">What happens next?</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• We'll verify your ownership documents</li>
                  <li>• You'll receive an email confirmation within 24 hours</li>
                  <li>• Once approved, you can manage your listing</li>
                </ul>
              </div>
              <Link href="/">
                <Button>Return to Directory</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center text-blue-600 hover:text-blue-800">
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Directory
            </Link>
            <h1 className="text-xl font-semibold text-gray-900">Claim Business</h1>
            <div></div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Business Info Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardHeader>
                <CardTitle>Business Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <div className="font-medium">{mockBusiness.name}</div>
                    <div className="text-sm text-gray-600">Business Name</div>
                  </div>
                  <div>
                    <div className="font-medium">{mockBusiness.address}</div>
                    <div className="text-sm text-gray-600">Address</div>
                  </div>
                  <div>
                    <div className="font-medium">{mockBusiness.phone}</div>
                    <div className="text-sm text-gray-600">Phone</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Claim Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Claim Your Business Listing</CardTitle>
                <p className="text-sm text-gray-600">
                  Fill out the form below to claim ownership of this business listing. We'll verify your information and
                  approve your claim within 2-3 business days.
                </p>
              </CardHeader>
              <CardContent>
                <Alert className="mb-6">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    You must be the owner or authorized representative of this business to claim this listing.
                  </AlertDescription>
                </Alert>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Personal Information */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Personal Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName">First Name *</Label>
                        <Input
                          id="firstName"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="lastName">Last Name *</Label>
                        <Input
                          id="lastName"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="email">Email Address *</Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="phone">Phone Number *</Label>
                        <Input
                          id="phone"
                          name="phone"
                          type="tel"
                          value={formData.phone}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                    </div>
                  </div>

                  {/* Business Relationship */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Business Relationship</h3>
                    <div>
                      <Label htmlFor="relationship">Your relationship to this business *</Label>
                      <Input
                        id="relationship"
                        name="relationship"
                        placeholder="e.g., Owner, Manager, Authorized Representative"
                        value={formData.relationship}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                  </div>

                  {/* Business Contact */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Business Contact Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="businessEmail">Business Email</Label>
                        <Input
                          id="businessEmail"
                          name="businessEmail"
                          type="email"
                          value={formData.businessEmail}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div>
                        <Label htmlFor="businessPhone">Business Phone</Label>
                        <Input
                          id="businessPhone"
                          name="businessPhone"
                          type="tel"
                          value={formData.businessPhone}
                          onChange={handleInputChange}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Supporting Documents */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Supporting Documents</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      Please upload documents that verify your ownership or authorization to manage this business:
                    </p>
                    <ul className="text-sm text-gray-600 mb-4 list-disc list-inside">
                      <li>Business license</li>
                      <li>Articles of incorporation</li>
                      <li>Tax documents</li>
                      <li>Utility bills</li>
                      <li>Other official business documents</li>
                    </ul>
                    <div>
                      <Label htmlFor="documents">Upload Documents</Label>
                      <Input
                        id="documents"
                        type="file"
                        multiple
                        accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                        onChange={handleFileUpload}
                        className="mt-1"
                      />
                    </div>
                    {documents.length > 0 && (
                      <div className="mt-2">
                        <p className="text-sm font-medium">Uploaded files:</p>
                        <ul className="text-sm text-gray-600">
                          {documents.map((file, index) => (
                            <li key={index} className="flex items-center">
                              <Upload className="h-3 w-3 mr-1" />
                              {file.name}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  {/* Additional Message */}
                  <div>
                    <Label htmlFor="message">Additional Information (Optional)</Label>
                    <Textarea
                      id="message"
                      name="message"
                      placeholder="Provide any additional information that might help us verify your claim..."
                      value={formData.message}
                      onChange={handleInputChange}
                      rows={4}
                    />
                  </div>

                  {/* Agreement */}
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="agreement"
                      checked={agreed}
                      onCheckedChange={(checked) => setAgreed(checked as boolean)}
                    />
                    <Label htmlFor="agreement" className="text-sm">
                      I certify that I am the owner or authorized representative of this business and that all
                      information provided is accurate. I understand that providing false information may result in the
                      rejection of my claim.
                    </Label>
                  </div>

                  {/* Submit Button */}
                  <Button type="submit" className="w-full" disabled={!agreed}>
                    Submit Claim Request
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
