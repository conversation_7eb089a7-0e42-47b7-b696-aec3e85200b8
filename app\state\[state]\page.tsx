"use client"

import { useState } from "react"
import { ArrowLeft, Search, MapPin, Star } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Link from "next/link"
import { useParams } from "next/navigation"

// Mock data for state page
const stateData = {
  fl: {
    name: "Florida",
    totalBusinesses: 1247,
    cities: [
      { name: "Miami", count: 234 },
      { name: "Orlando", count: 189 },
      { name: "Tampa", count: 156 },
      { name: "Jacksonville", count: 143 },
      { name: "Fort Lauderdale", count: 98 },
      { name: "Tallahassee", count: 87 },
    ],
  },
}

const mockBusinesses = [
  {
    id: "1",
    name: "Elite Pressure Washing",
    city: "Miami",
    state: "FL",
    description: "Professional residential and commercial pressure washing services with 15+ years of experience.",
    phone: "(*************",
    rating: 4.8,
    reviewCount: 127,
    services: ["Residential", "Commercial", "Roof Cleaning"],
    claimed: true,
    image: "/placeholder.svg?height=200&width=300",
  },
  {
    id: "2",
    name: "Crystal Clean Power Wash",
    city: "Orlando",
    state: "FL",
    description: "Eco-friendly pressure washing solutions for homes and businesses throughout Central Florida.",
    phone: "(*************",
    rating: 4.6,
    reviewCount: 89,
    services: ["Residential", "Commercial", "Pool Deck Cleaning"],
    claimed: false,
    image: "/placeholder.svg?height=200&width=300",
  },
  {
    id: "3",
    name: "ProWash Solutions",
    city: "Tampa",
    state: "FL",
    description: "Complete exterior cleaning services including pressure washing, soft washing, and gutter cleaning.",
    phone: "(*************",
    rating: 4.9,
    reviewCount: 203,
    services: ["Residential", "Commercial", "Soft Washing"],
    claimed: true,
    image: "/placeholder.svg?height=200&width=300",
  },
]

export default function StatePage() {
  const params = useParams()
  const stateCode = params.state as string
  const state = stateData[stateCode as keyof typeof stateData]

  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCity, setSelectedCity] = useState("All Cities")
  const [sortBy, setSortBy] = useState("name")

  if (!state) {
    return <div>State not found</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center text-blue-600 hover:text-blue-800">
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Directory
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">Pressure Washing in {state.name}</h1>
            <div></div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* State Overview */}
        <div className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="text-3xl">Pressure Washing Services in {state.name}</CardTitle>
              <p className="text-lg text-gray-600">
                Find {state.totalBusinesses.toLocaleString()} professional pressure washing businesses across{" "}
                {state.name}
              </p>
            </CardHeader>
          </Card>
        </div>

        {/* Cities Grid */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Browse by City</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {state.cities.map((city) => (
              <Link key={city.name} href={`/city/${stateCode}/${city.name.toLowerCase().replace(" ", "-")}`}>
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardContent className="p-4 text-center">
                    <div className="text-lg font-semibold text-gray-900">{city.name}</div>
                    <div className="text-sm text-gray-600">{city.count} businesses</div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <Input
                      placeholder="Search businesses..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={selectedCity} onValueChange={setSelectedCity}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Select City" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All Cities">All Cities</SelectItem>
                    {state.cities.map((city) => (
                      <SelectItem key={city.name} value={city.name}>
                        {city.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Sort By" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="rating">Rating</SelectItem>
                    <SelectItem value="city">City</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Business Listings */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockBusinesses.map((business) => (
            <Card key={business.id} className="hover:shadow-xl transition-shadow">
              <div className="relative">
                <img
                  src={business.image || "/placeholder.svg"}
                  alt={business.name}
                  className="w-full h-48 object-cover rounded-t-lg"
                />
                {business.claimed && <Badge className="absolute top-2 right-2 bg-green-500">Verified</Badge>}
              </div>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <CardTitle className="text-xl">{business.name}</CardTitle>
                  <div className="flex items-center">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="ml-1 text-sm font-medium">{business.rating}</span>
                    <span className="ml-1 text-sm text-gray-500">({business.reviewCount})</span>
                  </div>
                </div>
                <div className="flex items-center text-gray-600">
                  <MapPin className="h-4 w-4 mr-1" />
                  {business.city}, {business.state}
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4 line-clamp-2">{business.description}</p>

                <div className="flex flex-wrap gap-2 mb-4">
                  {business.services.slice(0, 3).map((service) => (
                    <Badge key={service} variant="secondary">
                      {service}
                    </Badge>
                  ))}
                </div>

                <div className="flex gap-2">
                  <Link href={`/business/${business.id}`} className="flex-1">
                    <Button variant="outline" className="w-full bg-transparent">
                      View Details
                    </Button>
                  </Link>
                  {!business.claimed && (
                    <Link href={`/claim/${business.id}`}>
                      <Button variant="default">Claim</Button>
                    </Link>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Load More */}
        <div className="text-center mt-12">
          <Button variant="outline" size="lg">
            Load More Businesses
          </Button>
        </div>
      </div>
    </div>
  )
}
